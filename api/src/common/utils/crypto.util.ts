import * as crypto from 'crypto';
import { parsedEnv } from 'src/env/env.schema';

const algorithm = 'aes-256-cbc';
const ivLength = 16;
const key = crypto
  .createHash('sha256')
  .update(String(parsedEnv.JWT_SECRET))
  .digest();

/**
 * Encrypts a given text using AES-256-CBC algorithm.
 * Generates a random initialization vector (IV) for each encryption.
 * Returns the IV and encrypted data as a hex string separated by a colon.
 *
 * @param text - The plaintext string to encrypt.
 * @returns The encrypted text in the format "ivHex:encryptedHex".
 */
export function encrypt(text: string): string {
  const iv = crypto.randomBytes(ivLength);
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  const encrypted = Buffer.concat([
    cipher.update(text, 'utf8'),
    cipher.final(),
  ]);
  return `${iv.toString('hex')}:${encrypted.toString('hex')}`;
}

/**
 * Decrypts an encrypted text string that contains the IV and encrypted data.
 * Splits the input by colon to extract the IV and encrypted content.
 * Uses AES-256-CBC algorithm and the derived key to decrypt the data.
 *
 * @param encryptedText - The encrypted text in the format "ivHex:encryptedHex".
 * @returns The decrypted plaintext string.
 */

export function decrypt(encryptedText: string): string {
  const [ivHex, encryptedHex] = encryptedText.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const encrypted = Buffer.from(encryptedHex, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  const decrypted = Buffer.concat([
    decipher.update(encrypted),
    decipher.final(),
  ]);
  return decrypted.toString('utf8');
}
/**
 * Generates a random password that meets MySQL password validation policy requirements.
 * The password will contain:
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one special character
 *
 * @param length - The desired length of the password (default is 32, minimum 8).
 * @returns A random password string that meets MySQL policy requirements.
 */

// Character sets for password generation
const CHAR_SETS = {
  uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  lowercase: 'abcdefghijklmnopqrstuvwxyz',
  numbers: '0123456789',
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
} as const;

const ALL_CHARS = Object.values(CHAR_SETS).join('');

/**
 * Generates a cryptographically secure random password that meets MySQL password validation policy.
 * Uses Fisher-Yates shuffle for better randomization.
 */
export function genPassword(length: number = 32): string {
  if (length < 8) {
    throw new Error('Password length must be at least 8 characters');
  }

  // Pre-allocate array for better performance
  const password = new Array<string>(length);

  // Ensure at least one character from each required category
  password[0] = getRandomChar(CHAR_SETS.uppercase);
  password[1] = getRandomChar(CHAR_SETS.lowercase);
  password[2] = getRandomChar(CHAR_SETS.numbers);
  password[3] = getRandomChar(CHAR_SETS.specialChars);

  // Fill remaining positions with random characters
  for (let i = 4; i < length; i++) {
    password[i] = getRandomChar(ALL_CHARS);
  }

  // Fisher-Yates shuffle for cryptographically secure randomization
  for (let i = password.length - 1; i > 0; i--) {
    const j = crypto.randomInt(0, i + 1);
    [password[i], password[j]] = [password[j], password[i]];
  }

  return password.join('');
}

/**
 * Helper function to get a random character from a character set
 */
function getRandomChar(charSet: string): string {
  return charSet[crypto.randomInt(0, charSet.length)];
}
