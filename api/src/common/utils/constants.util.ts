import { ValidationPipeOptions } from '@nestjs/common';
import { HeadersObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { ThrottlerModuleOptions } from '@nestjs/throttler';
import { parsedEnv } from 'src/env/env.schema';

export const ValidationOptions: ValidationPipeOptions = {
  whitelist: true,
  forbidNonWhitelisted: true,
  transform: true,
  transformOptions: {
    enableImplicitConversion: true,
  },
};

export const DefaultPageSize = {
  USER: 10,
  ORDERS: 5,
  CATEGORY: 30,
  PRODUCTS: 20,
} as const satisfies Record<string, number>;

export const ThrottleOptions: ThrottlerModuleOptions = [
  {
    ttl: parsedEnv.THROTTLER_TTL,
    limit: parsedEnv.THROTTLER_LIMIT,
  },
];

export const JwtCookieHeader: HeadersObject = {
  'Set-Cookie': {
    description: 'Jwt cookie',
    schema: { type: 'string' },
  },
};
