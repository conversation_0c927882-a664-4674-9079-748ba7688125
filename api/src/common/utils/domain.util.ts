import { BadRequestException } from '@nestjs/common';
import { resolve } from 'dns/promises';

/**
 * Domain and IP validation utilities
 */
export class DomainUtil {
  /**
   * Resolves a hostname to its IP addresses
   * @param hostname - The hostname to resolve
   * @returns Promise<string[]> - Array of IP addresses
   * @throws BadRequestException if hostname cannot be resolved
   */
  static async resolveHostname(hostname: string): Promise<string[]> {
    try {
      return await resolve(hostname);
    } catch {
      throw new BadRequestException(`Unable to resolve hostname: ${hostname}`);
    }
  }

  /**
   * Gets the first IP address for a hostname
   * @param hostname - The hostname to resolve
   * @returns Promise<string> - The first IP address
   * @throws BadRequestException if hostname cannot be resolved
   */
  static async getFirstIp(hostname: string): Promise<string> {
    const addresses = await this.resolveHostname(hostname);
    return addresses[0];
  }

  /**
   * Checks if a domain points to a specific IP address
   * @param domain - The domain to check
   * @param targetIp - The IP address to check against
   * @returns Promise<boolean> - True if domain points to the target IP
   */
  static async domainPointsToIp(
    domain: string,
    targetIp: string,
  ): Promise<boolean> {
    try {
      const domainIps = await resolve(domain);
      return domainIps.includes(targetIp);
    } catch {
      return false;
    }
  }

  /**
   * Validates that a domain points to a server's IP
   * @param domain - The domain to validate
   * @param serverHostname - The server hostname (e.g., "abuja.rapidstacks.live")
   * @returns Promise<{ isValid: boolean; serverIp: string; message?: string }>
   */
  static async validateDomainPointsToServer(
    domain: string,
    serverHostname: string,
  ): Promise<{ isValid: boolean; serverIp: string; message?: string }> {
    try {
      const serverIp = await this.getFirstIp(serverHostname);

      // Check if domain points directly to our server IP
      const directMatch = await this.domainPointsToIp(domain, serverIp);

      // If direct match, validation passes
      if (directMatch) {
        return {
          isValid: true,
          serverIp,
        };
      }

      // Check if domain is a subdomain of our managed domains
      const isOurSubdomain = this.isOurManagedSubdomain(domain, serverHostname);

      return {
        isValid: isOurSubdomain,
        serverIp,
        message: isOurSubdomain
          ? undefined
          : `Domain is not reachable. Please point your A record to our IP ${serverIp}`,
      };
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Domain validation failed',
      );
    }
  }

  /**
   * Checks if a domain is a subdomain of our managed domains
   * @param domain - The domain to check
   * @param serverHostname - The server hostname
   * @returns boolean - True if it's our managed subdomain
   */
  private static isOurManagedSubdomain(
    domain: string,
    serverHostname: string,
  ): boolean {
    // Extract base domain from server hostname (e.g., "dovely.tech" from "abuja.dovely.tech")
    const serverParts = serverHostname.split('.');
    if (serverParts.length < 2) return false;

    const baseDomain = serverParts.slice(-2).join('.'); // Get last 2 parts (dovely.tech)

    // Check if domain is a subdomain of our managed domains
    const managedDomains = [baseDomain, 'adun.studio']; // Add other managed domains here

    return managedDomains.some((managedDomain) => {
      return domain === managedDomain || domain.endsWith(`.${managedDomain}`);
    });
  }

  /**
   * Sanitizes a domain name for use as database name or file name
   * @param domain - The domain to sanitize
   * @returns string - Sanitized domain with dots and hyphens replaced by underscores
   */
  static sanitizeDomainForDb(domain: string): string {
    return domain.replace(/[.-]/g, '_');
  }

  /**
   * Validates domain format (basic validation)
   * @param domain - The domain to validate
   * @returns boolean - True if domain format is valid
   */
  static isValidDomainFormat(domain: string): boolean {
    const domainRegex =
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return domainRegex.test(domain) && domain.length <= 253;
  }
}
