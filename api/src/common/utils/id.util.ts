import { ArrayUniqueIdentifier } from 'class-validator';
import { OrderItemDto } from 'src/domain/orders/dto/order-item.dto';
import { IdDto } from '../dto/id.dto';

export function wrapId(idOrIds: string | string[]) {
  if (Array.isArray(idOrIds)) return idOrIds.map((id) => ({ id }));

  return { id: idOrIds };
}

export const IdentifierFn = {
  ID_DTO: (dto: IdDto) => dto.id,
  ORDER_ITEM_ID_DTO: (dto: OrderItemDto) => dto.product?.id,
} as const satisfies Record<string, ArrayUniqueIdentifier>;
