import { Logger } from '@nestjs/common';
import * as dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const logger = new Logger('Config');

export const envSchema = z.object({
  APP_PREFIX: z.string().default('api/v1'),
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z.coerce.number().default(4020),
  DATABASE_URL: z.string().url(),
  REDIS_HOST: z.string().min(1),
  REDIS_PORT: z.coerce.number().default(6379),
  REDIS_PASSWORD: z.string().optional(),
  JWT_SECRET: z
    .string()
    .min(32, 'JWT_SECRET must be at least 32 characters long'),
  JWT_TTL: z.string().default('15m'),
  THROTTLER_TTL: z.coerce.number().default(60000),
  THROTTLER_LIMIT: z.coerce.number().default(10),
  SSH_HOST: z.string().min(1),
  SSH_PORT: z.coerce.number().default(22),
  SSH_USER: z.string().min(1),
  SSH_KEY_PATH: z.string().min(1),
  SSH_SCRIPTS_DIR: z.string().min(1),
  SSH_SECRETS_DIR: z.string().min(1),
  SSH_SUDO_PASSWORD: z.string().optional(),
});

export const AppConfig = 'APP_CONFIG';
export type AppConfig = z.infer<typeof envSchema>;

export const parsedEnv = (() => {
  const result = envSchema.safeParse(process.env);
  if (!result.success) {
    logger.error('❌ Invalid environment configuration:');
    logger.error(JSON.stringify(result.error.format(), null, 2));
    process.exit(1);
  }
  return result.data;
})();
