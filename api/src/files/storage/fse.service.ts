import {
  ConflictException,
  Injectable,
  NotFoundException,
  StreamableFile,
} from '@nestjs/common';
import {
  createReadStream,
  mkdirp,
  pathExists,
  readdir,
  remove,
  writeFile,
} from 'fs-extra';
import { join } from 'path';
import { StorageService } from 'src/files/storage/storage.service';
import { UPLOAD_DIR } from 'src/files/utils/constants.util';

@Injectable()
export class FseService implements StorageService {
  async saveFile(path: string, file: Express.Multer.File): Promise<void> {
    const { buffer, originalname } = file;

    const uniqueFileName = this.genUniqueFileName(originalname);
    const fullPath = join(UPLOAD_DIR, path, uniqueFileName);
    await writeFile(fullPath, buffer);
  }

  getFile(path: string): StreamableFile {
    const fullPath = join(UPLOAD_DIR, path);
    const stream = createReadStream(fullPath);
    return new StreamableFile(stream);
  }

  async delete(path: string): Promise<void> {
    const fullPath = join(UPLOAD_DIR, path);
    await remove(fullPath);
  }

  async createDir(path: string): Promise<void> {
    const fullPath = join(UPLOAD_DIR, path);
    await mkdirp(fullPath);
  }

  getDirFiles(path: string): Promise<string[]> {
    const fullPath = join(UPLOAD_DIR, path);
    return readdir(fullPath);
  }

  async getDirFileCount(path: string): Promise<number> {
    return (await this.getDirFiles(path)).length;
  }

  async validatePath(path: string): Promise<void> {
    const fullPath = join(UPLOAD_DIR, path);
    if (!(await pathExists(fullPath)))
      throw new NotFoundException('Path not found');
  }

  validateFileCount(count: number, max: number): void {
    if (count > max)
      throw new ConflictException('File count exceeds max limit');
  }

  genUniqueFileName(filename: string): string {
    const uniquePrefix = `${Date.now()}-${Math.floor(Math.random() * 1e9)}`;
    return `${uniquePrefix}-${filename}`;
  }
}
