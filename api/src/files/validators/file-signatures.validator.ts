import { FileValidator } from '@nestjs/common';
import { filetypemime } from 'magic-bytes.js';

/** * Validates that the file type matches the file signature.
 * This validator checks the magic bytes of the file against its MIME type.
 * If the file's magic bytes do not match the expected MIME type,
 * it will return false, indicating a validation failure.
 */
export class FileSignatureValidator extends FileValidator {
  constructor() {
    super({});
  }
  buildErrorMessage(): string {
    return `Validation failed: file type does not match the file signature`;
  }

  isValid(file: Express.Multer.File): boolean {
    const getMimeTypes = filetypemime(file.buffer) as unknown as (
      bytes: number[] | Uint8Array | Uint8ClampedArray,
    ) => string[]; // types from the library

    const fileSignatures = getMimeTypes(file.buffer);

    if (!fileSignatures.length) return false;

    const isMatch = fileSignatures.includes(file.mimetype);
    if (!isMatch) return false;
    return true;
  }
}
