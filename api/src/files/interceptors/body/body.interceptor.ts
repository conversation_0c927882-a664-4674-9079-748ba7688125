import {
  BadRequestEx<PERSON>,
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class BodyInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    const request = context.switchToHttp().getRequest<Request>();
    try {
      if (typeof request.body !== 'string') {
        throw new BadRequestException('Request body must be a string');
      }
      const parsed = JSON.parse(request.body) as { data: unknown };
      request.body = parsed.data;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Invalid request body',
      );
    }
    return next.handle();
  }
}
