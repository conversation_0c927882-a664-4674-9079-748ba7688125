import {
  FileTypeValidator,
  FileValidator,
  HttpStatus,
  MaxFileSizeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import * as bytes from 'bytes';
import { lookup } from 'mime-types';
import { NonEmptyArray } from 'src/common/utils/array.util';
import { FileSignatureValidator } from 'src/files/validators/file-signatures.validator';

type FileSize = `${number}${'kb' | 'mb' | 'gb' | 'tb'}`;
type FileType = 'png' | 'jpeg' | 'webp' | 'svg' | 'pdf';

const createFileTypeRegex = (fileTypes: FileType[]): RegExp => {
  const mediaTypes = fileTypes.map((type) => lookup(type));
  return new RegExp(mediaTypes.join('|'));
};

/**
 * Creates an array of file validators for validating file size and type.
 *
 * @param maxSize - The maximum allowed file size (e.g., '2mb').
 * @param fileExtensions - The allowed file extensions (e.g., 'png', 'jpeg').
 * @returns An array of FileValidator instances.
 * @throws Error if the maxSize format is invalid.
 */
export const createFileValidator = (
  maxSize: FileSize,
  fileExtensions: NonEmptyArray<FileType>,
): FileValidator[] => {
  const fileTypeRegex = createFileTypeRegex(fileExtensions);
  const sizeInBytes = bytes(maxSize);

  if (sizeInBytes === null)
    throw new Error(`Invalid file size format: ${maxSize}`);

  return [
    new MaxFileSizeValidator({ maxSize: sizeInBytes }),
    new FileTypeValidator({ fileType: fileTypeRegex }),
    new FileSignatureValidator(),
  ];
};

export const createParseFilePipe = (
  maxSize: FileSize,
  ...fileTypes: NonEmptyArray<FileType>
) =>
  new ParseFilePipe({
    validators: createFileValidator(maxSize, fileTypes),
    errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  });
