import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { AppConfig } from 'src/env/env.schema';

export default {
  asProvider: () => ({
    inject: [AppConfig],
    useFactory: (config: AppConfig): TypeOrmModuleOptions => ({
      type: 'postgres',
      url: config.DATABASE_URL,
      autoLoadEntities: true,
      ssl:
        config.NODE_ENV === 'production'
          ? { rejectUnauthorized: false }
          : false,
    }),
  }),
};
