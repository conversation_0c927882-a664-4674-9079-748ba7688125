import { faker } from '@faker-js/faker';
import { Product } from 'src/domain/products/entities/product.entity';
import { setSeederFactory } from 'typeorm-extension';

export const ProductFactory = setSeederFactory(Product, () => {
  const product = new Product();

  product.name = faker.commerce.productName();
  product.description = faker.commerce.productDescription();
  product.price = parseFloat(
    faker.commerce.price({ min: 1000, max: 10000, dec: 2 }),
  );

  return product;
});
