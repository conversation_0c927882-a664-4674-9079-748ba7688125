import { faker } from '@faker-js/faker';
import { copyFile, mkdirp, writeFile } from 'fs-extra';
import { join } from 'path';
import { Category } from 'src/domain/categories/entities/category.entity';
import { OrderItem } from 'src/domain/orders/entities/order-item.entity';
import { Order } from 'src/domain/orders/entities/order.entity';
import { OrderStatus } from 'src/domain/orders/enums/order-status.enum';
import { Payment } from 'src/domain/payments/entities/payment.entity';
import { Product } from 'src/domain/products/entities/product.entity';
import { User } from 'src/domain/users/entities/user.entity';
import { FilePath, UPLOAD_DIR } from 'src/files/utils/constants.util';
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';

faker.seed(404);

export class MainSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<any> {
    console.info('Seeding users...');
    const userFactory = factoryManager.get(User);
    const users = await userFactory.saveMany(20);
    console.info('Users seeded:', users.length);

    console.info('Seeding categories...');
    const categoryFactory = factoryManager.get(Category);
    const categoryEntities = await Promise.all(
      Array.from({ length: 15 }).map(() => categoryFactory.make()),
    );

    // Ensuring unique category names
    const uniqueCategories = categoryEntities.reduce((acc, category) => {
      if (!acc.some((c) => c.name === category.name)) {
        acc.push(category);
      }
      return acc;
    }, [] as Category[]);

    const categoryRepo = dataSource.getRepository(Category);
    const categories = await categoryRepo.save(uniqueCategories);
    console.info('Categories seeded:', categories.length);

    console.info('Seeding products...');
    const productFactory = factoryManager.get(Product);
    const products = await Promise.all(
      Array.from({ length: 50 }).map(async () => {
        const product = productFactory.make({
          categories: faker.helpers.arrayElements(categories, {
            min: 1,
            max: 5,
          }),
        });

        return product;
      }),
    );

    const productRepo = dataSource.getRepository(Product);
    const savedProducts = await productRepo.save(products);
    console.info('Products seeded:', savedProducts.length);

    // Upload 2-5 images for each product by copying files to the upload directory
    const imageDir = join('src', 'db', 'seeding', 'images');
    // Generate 20 random commerce-related images using faker and download them
    const imageFiles: string[] = [];
    await mkdirp(imageDir);
    for (let i = 0; i < 20; i++) {
      const url = faker.image.urlLoremFlickr({
        category: 'commerce',
        width: 640,
        height: 480,
      });
      const filename = `${Date.now()}-commerce-${i + 1}.jpg`;
      const destPath = join(imageDir, filename);
      try {
        const res = await fetch(url);
        if (!res.ok) throw new Error(`Failed to fetch image: ${url}`);
        const arrayBuffer = await res.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        await writeFile(destPath, buffer);
        imageFiles.push(filename);
      } catch {
        // fallback: create an empty file if download fails
        await writeFile(destPath, Buffer.from(''));
        imageFiles.push(filename);
      }
    }
    const uploadBase = join(UPLOAD_DIR, FilePath.Products.BASE);
    for (const product of savedProducts) {
      const count = faker.number.int({ min: 2, max: 5 });
      const selected = faker.helpers.arrayElements(imageFiles, count);
      const productImageDir = join(
        uploadBase,
        String(product.id),
        FilePath.Products.IMAGES,
      );
      await mkdirp(productImageDir);
      for (const filename of selected) {
        const src = join(imageDir, filename);

        const uniqueName = `${Date.now()}-${Math.floor(Math.random() * 1e9)}-${filename}`;
        const dest = join(productImageDir, uniqueName);
        await copyFile(src, dest);
      }
    }
    console.info('Product images uploaded for all products.');

    console.info('Seeding orders...');
    const orderRepo = dataSource.getRepository(Order);
    const orderItemRepo = dataSource.getRepository(OrderItem);

    const orders = await Promise.all(
      users.slice(0, 30).map(async (user) => {
        const order = new Order();
        order.customer = user;

        // Save the order first to get its ID
        const savedOrder = await orderRepo.save(order);

        // Create order items with proper relationships
        const selectedProducts = faker.helpers.arrayElements(products, {
          min: 1,
          max: Math.min(5, products.length),
        });

        // Remove duplicates to avoid composite key violations
        const uniqueProducts = selectedProducts.filter(
          (product, index, self) =>
            index === self.findIndex((p) => p.id === product.id),
        );

        const orderItems = uniqueProducts.map((product) => {
          const orderItem = new OrderItem();
          orderItem.orderId = savedOrder.id;
          orderItem.productId = product.id;
          orderItem.order = savedOrder;
          orderItem.product = product;
          orderItem.quantity = faker.number.int({ min: 1, max: 10 });
          orderItem.price = product.price;
          return orderItem;
        });

        // Save order items
        await orderItemRepo.save(orderItems);

        // Update the order with items
        savedOrder.items = orderItems;
        return savedOrder;
      }),
    );
    console.info('Orders seeded:', orders.length);

    console.info('Seeding payments...');
    const paymentRepo = dataSource.getRepository(Payment);
    const selectedOrders = faker.helpers.arrayElements(orders, 10);

    const payments = await Promise.all(
      selectedOrders.map(async (order) => {
        const payment = paymentRepo.create();
        payment.order = order;
        order.payment = payment;
        order.status = OrderStatus.AWAITING_SHIPPING;

        await paymentRepo.save(payment);
        return await orderRepo.save(order);
      }),
    );
    console.info('Payments seeded:', payments.length);
  }
}
