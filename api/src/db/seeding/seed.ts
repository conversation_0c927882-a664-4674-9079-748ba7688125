import * as dotenv from 'dotenv';
import { expand } from 'dotenv-expand';
import { DataSource, DataSourceOptions } from 'typeorm';
import { runSeeders, SeederOptions } from 'typeorm-extension';

import { parsedEnv } from 'src/env/env.schema';
import { CategoryFactory } from './categories.factory';
import { MainSeeder } from './main.seeder';
import { ProductFactory } from './products.factory';
import { UserFactory } from './users.factory';

expand(dotenv.config());

const options: DataSourceOptions & SeederOptions = {
  type: 'postgres',
  url: parsedEnv.DATABASE_URL,
  entities: ['src/domain/**/*.entity.ts'],
  factories: [UserFactory, CategoryFactory, ProductFactory],
  seeds: [MainSeeder],
};

const datasource = new DataSource(options);

datasource
  .initialize()
  .then(async () => {
    console.info('Database connection established');
    await datasource.dropDatabase();
    await datasource.synchronize();
    await runSeeders(datasource);
    console.info('Seeding completed successfully');
    process.exit();
  })
  .catch((error) => {
    console.error('Error during seeding:', error);
    process.exit(1);
  });
