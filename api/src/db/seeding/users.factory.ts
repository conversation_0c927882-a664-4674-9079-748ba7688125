import { fakerEN_NG as faker } from '@faker-js/faker';
import * as argon from 'argon2';
import { User } from 'src/domain/users/entities/user.entity';
import { setSeederFactory } from 'typeorm-extension';

export const UserFactory = setSeederFactory(User, async () => {
  const user = new User();
  user.firstName = faker.person.firstName();
  user.lastName = faker.person.lastName();
  user.phone = faker.phone.number({ style: 'national' });
  user.email = faker.internet
    .email({
      allowSpecialCharacters: false,
      firstName: user.firstName,
      lastName: user.lastName,
    })
    .toLocaleLowerCase();
  const plainPassword = faker.internet.password({
    length: 12,
    memorable: false,
    pattern: /[A-Z0-9!@#$%^&*()_+\-=]/,
    prefix: 'A1!a', // ensure strong password
  });
  user.password = await argon.hash(plainPassword);
  return user;
});
