import * as dotenv from 'dotenv';
import * as dotenvExpand from 'dotenv-expand';
import { parsedEnv } from 'src/env/env.schema';
import { DataSource } from 'typeorm';

dotenvExpand.expand(dotenv.config());

const dataSource = new DataSource({
  type: 'postgres',
  url: parsedEnv.DATABASE_URL,
  entities: ['src/domain/**/*.entity.ts'],
});

const reset = async () => {
  await dataSource.initialize();
  await dataSource.dropDatabase();
  await dataSource.destroy();
  console.log('Database dropped!');
};

reset().catch((err) => {
  console.error('Error dropping database:', err);
  process.exit(1);
});
