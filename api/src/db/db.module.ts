import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import dbConfig from './config/db.config';
import { NotFoundExceptionFilter } from './filters/not-found.filter';

@Module({
  imports: [TypeOrmModule.forRootAsync(dbConfig.asProvider())],
  providers: [
    {
      provide: APP_FILTER,
      useClass: NotFoundExceptionFilter,
    },
  ],
})
export class DbModule {}
