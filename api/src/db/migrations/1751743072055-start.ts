import { MigrationInterface, QueryRunner } from "typeorm";

export class Start1751743072055 implements MigrationInterface {
    name = 'Start1751743072055'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "payment" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "orderId" uuid NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "REL_d09d285fe1645cd2f0db811e29" UNIQUE ("orderId"),
                CONSTRAINT "PK_fcaec7df5adf9cac408c686b2ab" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "category" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying(50) NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_23c05c292c439d77b0de816b500" UNIQUE ("name"),
                CONSTRAINT "PK_9c4e4a89e3674fc9f382d733f03" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "product" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying(100) NOT NULL,
                "description" character varying,
                "price" numeric(10, 2) NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_22cc43e9a74d7498546e9a63e77" UNIQUE ("name"),
                CONSTRAINT "PK_bebc9158e480b949565b4dc7a82" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "order_item" (
                "order_id" uuid NOT NULL,
                "product_id" uuid NOT NULL,
                "quantity" integer NOT NULL,
                "price" numeric(10, 2) NOT NULL,
                "deleted_at" TIMESTAMP,
                CONSTRAINT "PK_f72b2c61968a218d1756d37efdc" PRIMARY KEY ("order_id", "product_id")
            )
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."order_status_enum" AS ENUM(
                'AWAITING_PAYMENT',
                'AWAITING_SHIPPING',
                'SHIPPED',
                'IN_TRANSIT',
                'DELIVERED',
                'CANCELLED'
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "order" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "status" "public"."order_status_enum" NOT NULL DEFAULT 'AWAITING_PAYMENT',
                "customerId" uuid NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "PK_1031171c13130102495201e3e20" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."site_type" AS ENUM('wordpress', 'container')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."site_status" AS ENUM('active', 'disabled')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."server" AS ENUM(
                'Abakaliki',
                'Abeokuta',
                'Abuja',
                'Ado Ekiti',
                'Akure',
                'Asaba',
                'Awka',
                'Bauchi',
                'Benin City',
                'Birnin Kebbi',
                'Calabar',
                'Damaturu',
                'Dutse',
                'Enugu',
                'Gombe',
                'Gusau',
                'Ibadan',
                'Ikeja',
                'Ilorin',
                'Jalingo',
                'Jos',
                'Kaduna',
                'Kano',
                'Katsina',
                'Lafia',
                'Lokoja',
                'Maiduguri',
                'Makurdi',
                'Minna',
                'Oshogbo',
                'Owerri',
                'Port Harcourt',
                'Sokoto',
                'Umuahia',
                'Uyo',
                'Yenagoa',
                'Yola'
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "sites" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "domain" character varying(255) NOT NULL,
                "title" character varying(255) NOT NULL DEFAULT 'Ultra-fast hosting from Onclouds',
                "tagline" character varying(255) NOT NULL DEFAULT 'Built for speed. Powered by simplicity.',
                "type" "public"."site_type" NOT NULL DEFAULT 'wordpress',
                "status" "public"."site_status" NOT NULL DEFAULT 'active',
                "tag" character varying(50) NOT NULL,
                "adminUser" character varying(255) NOT NULL,
                "adminEmail" character varying(255) NOT NULL,
                "adminPassword" character varying(255) NOT NULL,
                "dbName" character varying(255) NOT NULL,
                "dbUser" character varying(255) NOT NULL,
                "dbPassword" character varying(255) NOT NULL,
                "dbHost" character varying(255) NOT NULL DEFAULT 'localhost',
                "server" "public"."server" NOT NULL,
                "ownerId" uuid,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_4578b679503e1b86cc1c2531b93" UNIQUE ("domain"),
                CONSTRAINT "PK_4f5eccb1dfde10c9170502595a7" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."role" AS ENUM('admin', 'manager', 'user')
        `);
        await queryRunner.query(`
            CREATE TABLE "users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "email" character varying(255) NOT NULL,
                "password" character varying(255) NOT NULL,
                "first_name" character varying(255),
                "last_name" character varying(255),
                "role" "public"."role" NOT NULL DEFAULT 'user',
                "phone" character varying(15),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
                CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TABLE "product_categories" (
                "productId" uuid NOT NULL,
                "categoryId" uuid NOT NULL,
                CONSTRAINT "PK_e65c1adebf00d61f1c84a4f3950" PRIMARY KEY ("productId", "categoryId")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_6156a79599e274ee9d83b1de13" ON "product_categories" ("productId")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_fdef3adba0c284fd103d0fd369" ON "product_categories" ("categoryId")
        `);
        await queryRunner.query(`
            ALTER TABLE "payment"
            ADD CONSTRAINT "FK_d09d285fe1645cd2f0db811e293" FOREIGN KEY ("orderId") REFERENCES "order"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "order_item"
            ADD CONSTRAINT "FK_e9674a6053adbaa1057848cddfa" FOREIGN KEY ("order_id") REFERENCES "order"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "order_item"
            ADD CONSTRAINT "FK_5e17c017aa3f5164cb2da5b1c6b" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "order"
            ADD CONSTRAINT "FK_124456e637cca7a415897dce659" FOREIGN KEY ("customerId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ADD CONSTRAINT "FK_b00408635dde2a3b0cb6e57de17" FOREIGN KEY ("ownerId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "product_categories"
            ADD CONSTRAINT "FK_6156a79599e274ee9d83b1de139" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE
        `);
        await queryRunner.query(`
            ALTER TABLE "product_categories"
            ADD CONSTRAINT "FK_fdef3adba0c284fd103d0fd3697" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "product_categories" DROP CONSTRAINT "FK_fdef3adba0c284fd103d0fd3697"
        `);
        await queryRunner.query(`
            ALTER TABLE "product_categories" DROP CONSTRAINT "FK_6156a79599e274ee9d83b1de139"
        `);
        await queryRunner.query(`
            ALTER TABLE "sites" DROP CONSTRAINT "FK_b00408635dde2a3b0cb6e57de17"
        `);
        await queryRunner.query(`
            ALTER TABLE "order" DROP CONSTRAINT "FK_124456e637cca7a415897dce659"
        `);
        await queryRunner.query(`
            ALTER TABLE "order_item" DROP CONSTRAINT "FK_5e17c017aa3f5164cb2da5b1c6b"
        `);
        await queryRunner.query(`
            ALTER TABLE "order_item" DROP CONSTRAINT "FK_e9674a6053adbaa1057848cddfa"
        `);
        await queryRunner.query(`
            ALTER TABLE "payment" DROP CONSTRAINT "FK_d09d285fe1645cd2f0db811e293"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_fdef3adba0c284fd103d0fd369"
        `);
        await queryRunner.query(`
            DROP INDEX "public"."IDX_6156a79599e274ee9d83b1de13"
        `);
        await queryRunner.query(`
            DROP TABLE "product_categories"
        `);
        await queryRunner.query(`
            DROP TABLE "users"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."role"
        `);
        await queryRunner.query(`
            DROP TABLE "sites"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."server"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."site_status"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."site_type"
        `);
        await queryRunner.query(`
            DROP TABLE "order"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."order_status_enum"
        `);
        await queryRunner.query(`
            DROP TABLE "order_item"
        `);
        await queryRunner.query(`
            DROP TABLE "product"
        `);
        await queryRunner.query(`
            DROP TABLE "category"
        `);
        await queryRunner.query(`
            DROP TABLE "payment"
        `);
    }

}
