import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingState1751983176395 implements MigrationInterface {
    name = 'PendingState1751983176395'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TYPE "public"."site_status"
            RENAME TO "site_status_old"
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."site_status" AS ENUM('pending', 'active', 'disabled')
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status" TYPE "public"."site_status" USING "status"::"text"::"public"."site_status"
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status"
            SET DEFAULT 'active'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."site_status_old"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."site_status_old" AS ENUM('active', 'disabled')
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status" DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status" TYPE "public"."site_status_old" USING "status"::"text"::"public"."site_status_old"
        `);
        await queryRunner.query(`
            ALTER TABLE "sites"
            ALTER COLUMN "status"
            SET DEFAULT 'active'
        `);
        await queryRunner.query(`
            DROP TYPE "public"."site_status"
        `);
        await queryRunner.query(`
            ALTER TYPE "public"."site_status_old"
            RENAME TO "site_status"
        `);
    }

}
