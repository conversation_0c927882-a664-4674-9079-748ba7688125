import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Query,
} from '@nestjs/common';
import { IdDto } from 'src/common/dto/id.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { RemoveDto } from 'src/common/dto/remove.dto';
import { Roles } from 'src/domain/auth/decorators/role.decorator';
import { whoami } from 'src/domain/auth/decorators/whoami.decorator';
import { AuthDto } from 'src/domain/auth/dto/auth.dto';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { Role } from 'src/domain/auth/roles/role.enum';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersService } from './users.service';

@Controller('accounts')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Roles(Role.ADMIN)
  @Get()
  findAll(@Query() pagination: PaginationDto) {
    return this.usersService.findAll(pagination);
  }

  @Get('profile')
  profile(@whoami() { id }: RequestUser) {
    return this.usersService.findOne(id);
  }

  @Get(':id')
  findOne(@Param() { id }: IdDto) {
    return this.usersService.findOne(id);
  }

  @Patch('recover')
  recover(dto: AuthDto) {
    return this.usersService.recover(dto);
  }

  @Patch(':id')
  update(
    @Param() { id }: IdDto,
    @Body() dto: UpdateUserDto,
    @whoami() user: RequestUser,
  ) {
    return this.usersService.update(id, dto, user);
  }

  @Delete(':id')
  remove(
    @Param() { id }: IdDto,
    @Query() { soft }: RemoveDto,
    @whoami() user: RequestUser,
  ) {
    return this.usersService.remove(id, soft, user);
  }
}
