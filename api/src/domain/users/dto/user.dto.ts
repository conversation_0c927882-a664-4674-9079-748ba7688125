import { IsEmail, IsOptional, IsString, Length } from 'class-validator';
import { IsPassword } from 'src/common/decorators/validators/is-password.decorator';

export class UserDto {
  @IsEmail()
  email!: string;

  /**
   * Requires:
   * 1. 8 to 20 characters
   * 2. At least one
   * - Lowercase letter
   * - Uppercase letter
   * - Number
   * - Special character
   */
  @IsPassword()
  password: string;

  @IsString()
  @Length(2, 255, {
    message: 'First name must be between 2 and 255 characters.',
  })
  @IsOptional()
  firstName?: string;

  @IsString()
  @Length(2, 255, {
    message: 'Last name must be between 2 and 255 characters.',
  })
  @IsOptional()
  lastName?: string;

  @IsOptional()
  phone?: string;
}
