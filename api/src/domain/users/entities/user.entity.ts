import { RegistryDates } from 'src/common/embedded/registry-dates.embedded';
import { Role } from 'src/domain/auth/roles/role.enum';
import { Order } from 'src/domain/orders/entities/order.entity';
import { Site } from 'src/domain/sites/entities/site.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  password: string;

  @Column({ name: 'first_name', type: 'varchar', length: 255, nullable: true })
  firstName?: string;

  @Column({ name: 'last_name', type: 'varchar', length: 255, nullable: true })
  lastName?: string;

  @Column({ type: 'enum', enum: Role, enumName: 'role', default: Role.USER })
  role: Role;

  @Column({ type: 'varchar', length: 15, nullable: true })
  phone?: string;

  @Column(() => RegistryDates, { prefix: false })
  registryDates: RegistryDates;

  @OneToMany(() => Site, (site) => site.owner, {
    cascade: ['soft-remove', 'recover'],
  })
  sites: Site[];

  @OneToMany(() => Order, (order) => order.customer, {
    cascade: ['soft-remove', 'recover'],
  })
  orders: Order[];

  get IsDeleted() {
    return !!this.registryDates.deletedAt;
  }
}
