import {
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { compareIds } from 'src/common/utils/compare.util';
import { DefaultPageSize } from 'src/common/utils/constants.util';
import { AuthDto } from 'src/domain/auth/dto/auth.dto';
import { HashingService } from 'src/domain/auth/hashing/hashing.service';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { Role } from 'src/domain/auth/roles/role.enum';
import { Repository } from 'typeorm';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly hashingService: HashingService,
  ) {}

  findAll(pagination: PaginationDto) {
    return this.userRepo.find({
      skip: pagination.offset,
      take: pagination.limit ?? DefaultPageSize.USER,
    });
  }

  async findOne(id: string) {
    return await this.userRepo.findOneOrFail({
      where: { id },
      relations: { orders: { items: true, payment: true } },
    });
  }

  async update(id: string, dto: UpdateUserDto, currentUser: RequestUser) {
    if (currentUser.role !== Role.ADMIN) compareIds(currentUser.id, id);

    const user = await this.userRepo.preload({ id, ...dto });
    if (!user) throw new NotFoundException('User not found');
    return this.userRepo.save(user);
  }

  async remove(id: string, soft: boolean, currentUser: RequestUser) {
    if (currentUser.role !== Role.ADMIN) compareIds(currentUser.id, id);

    if (!soft) throw new ForbiddenException('Resource denied!');

    const user = await this.findOne(id);
    return soft ? this.userRepo.softRemove(user) : this.userRepo.remove(user);
  }

  async recover(dto: AuthDto) {
    const { email, password } = dto;
    const user = await this.userRepo.findOne({
      where: { email },
      relations: { orders: { items: true, payment: true } },
      withDeleted: true,
    });

    if (!user) throw new UnauthorizedException('Invalid credentials');

    const isMatch = await this.hashingService.compare(user.password, password);

    if (!isMatch) throw new UnauthorizedException('Invalid credentials');

    if (!user.IsDeleted) throw new ConflictException('User not deleted');

    return this.userRepo.save(user);
  }
}
