import { ConflictException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { compareIds } from 'src/common/utils/compare.util';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { Role } from 'src/domain/auth/roles/role.enum';
import { Order } from 'src/domain/orders/entities/order.entity';
import { OrderStatus } from 'src/domain/orders/enums/order-status.enum';
import { Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepo: Repository<Payment>,
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
  ) {}

  async payOrder(id: string, currentUser: RequestUser) {
    const order = await this.orderRepo.findOneOrFail({
      where: { id },
      relations: { payment: true, customer: true },
    });

    if (currentUser.role !== Role.ADMIN) compareIds(currentUser.id, order.id);

    if (order.payment) throw new ConflictException('Order already paid');

    const payment = this.paymentRepo.create();
    order.payment = payment;
    order.status = OrderStatus.AWAITING_SHIPPING;

    return this.orderRepo.save(order);
  }
}
