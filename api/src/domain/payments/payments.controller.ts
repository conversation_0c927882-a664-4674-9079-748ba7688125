import { <PERSON>, <PERSON><PERSON>, Post } from '@nestjs/common';
import { IdDto } from 'src/common/dto/id.dto';
import { whoami } from 'src/domain/auth/decorators/whoami.decorator';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { PaymentsService } from './payments.service';

@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentService: PaymentsService) {}

  @Post(':id')
  create(@Param() { id }: IdDto, @whoami() user: RequestUser) {
    return this.paymentService.payOrder(id, user);
  }
}
