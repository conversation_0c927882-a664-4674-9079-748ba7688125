import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOkResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { IdDto } from 'src/common/dto/id.dto';
import { JwtCookieHeader } from 'src/common/utils/constants.util';
import { Roles } from 'src/domain/auth/decorators/role.decorator';
import { AuthDto } from 'src/domain/auth/dto/auth.dto';
import { RolesDto } from 'src/domain/auth/dto/role.dto';
import { Role } from 'src/domain/auth/roles/role.enum';
import { AppConfig } from 'src/env/env.schema';
import { AuthService } from './auth.service';
import { Public } from './decorators/public.decorator';
import { whoami } from './decorators/whoami.decorator';
import { LocalAuthGuard } from './guards/local.guard';
import { RequestUser } from './interfaces/request-user.interface';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    @Inject(AppConfig)
    private readonly config: AppConfig,
  ) {}

  @HttpCode(HttpStatus.CREATED)
  @Public()
  @Post('signup')
  signup(@Body() dto: AuthDto) {
    return this.authService.signup(dto);
  }

  @ApiBody({ type: AuthDto })
  @ApiOkResponse({ headers: JwtCookieHeader })
  @HttpCode(HttpStatus.OK)
  @UseGuards(LocalAuthGuard)
  @Public()
  @Post('signin')
  signin(
    @whoami() user: RequestUser,
    @Res({ passthrough: true }) res: Response,
  ) {
    const token = this.authService.login(user);
    res.cookie('jwt', token, {
      httpOnly: true,
      secure: this.config.NODE_ENV === 'production',
      sameSite: true,
    });
  }

  @Roles(Role.ADMIN)
  @Patch(':id/assign')
  assign(@Param() { id }: IdDto, @Body() { role }: RolesDto) {
    return this.authService.assign(id, role);
  }
}
