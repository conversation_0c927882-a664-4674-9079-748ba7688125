import { Injectable } from '@nestjs/common';

@Injectable()
export abstract class HashingService {
  /**
   * Hashes the given data.
   * @param data - The data to hash, can be a string or Buffer.
   * @returns A promise that resolves to the hashed string.
   */
  abstract hash(data: string | Buffer): Promise<string>;

  /**
   * Compares the encrypted string with the provided data.
   * @param encrypted - The encrypted string to compare against.
   * @param data - The data to compare, can be a string or Buffer.
   * @returns A promise that resolves to a boolean indicating if the comparison is successful.
   */
  abstract compare(encrypted: string, data: string | Buffer): Promise<boolean>;
}
