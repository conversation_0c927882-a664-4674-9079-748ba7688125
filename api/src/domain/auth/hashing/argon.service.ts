import { Injectable } from '@nestjs/common';
import * as argon from 'argon2';
import { HashingService } from './hashing.service';

@Injectable()
export class ArgonService implements HashingService {
  async hash(data: string | Buffer): Promise<string> {
    return argon.hash(data);
  }

  async compare(encrypted: string, data: string | Buffer): Promise<boolean> {
    return argon.verify(encrypted, data);
  }
}
