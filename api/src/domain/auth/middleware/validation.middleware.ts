import {
  BadRequestException,
  Injectable,
  NestMiddleware,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { NextFunction, Request, Response } from 'express';
import { AuthDto } from '../dto/auth.dto';

@Injectable()
export class AuthValidationMiddleware implements NestMiddleware {
  async use(req: Request, res: Response, next: NextFunction) {
    const authDto = plainToInstance(AuthDto, req.body);

    const errors = await validate(authDto, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });

    if (errors.length) {
      const errorMessages = errors.map((error) =>
        Object.values(error.constraints || []).join(),
      );
      throw new BadRequestException(errorMessages);
    }
    next();
  }
}
