import {
  ConflictException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from 'src/domain/auth/roles/role.enum';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { AuthDto } from './dto/auth.dto';
import { HashingService } from './hashing/hashing.service';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { RequestUser } from './interfaces/request-user.interface';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly hashingService: HashingService,
    private readonly jwtService: JwtService,
  ) {}

  async signup(dto: AuthDto) {
    const existed = await this.userRepo.findOneBy({ email: dto.email });

    if (existed)
      throw new ConflictException('Account already exists. Please sign in.');

    const hashed = await this.hashingService.hash(dto.password);
    const user = this.userRepo.create({ ...dto, password: hashed });
    const savedUser = await this.userRepo.save(user);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = savedUser;
    return result;
  }

  async validateLocal(dto: AuthDto) {
    const user = await this.userRepo.findOne({
      where: { email: dto.email },
      select: { id: true, password: true },
    });

    if (!user) throw new UnauthorizedException('Invalid credentials');

    const isMatch = await this.hashingService.compare(
      user.password,
      dto.password,
    );

    if (!isMatch) throw new UnauthorizedException('Invalid credentials');

    return this.createRequestUser(user);
  }

  async validateJwt(payload: JwtPayload): Promise<RequestUser> {
    const user = await this.userRepo.findOneBy({ id: payload.sub });
    if (!user) throw new UnauthorizedException('Invalid token');
    return this.createRequestUser(user);
  }

  login(user: RequestUser) {
    const payload: JwtPayload = { sub: user.id };
    return this.jwtService.sign(payload);
  }

  async assign(id: string, role: Role) {
    const user = await this.userRepo.preload({ id, role });
    if (!user) throw new NotFoundException('User not found');
    return this.userRepo.save(user);
  }

  private createRequestUser(user: User) {
    const { id, role } = user;
    const requestUser: RequestUser = { id, role };
    return requestUser;
  }
}
