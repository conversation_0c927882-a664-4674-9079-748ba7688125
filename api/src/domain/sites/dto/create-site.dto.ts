import { IsEmail, IsEnum, IsOptional, IsString, Length } from 'class-validator';
import { IsPassword } from 'src/common/decorators/validators/is-password.decorator';
import { SiteType } from '../entities/site.entity';
import { Server } from '../enums/servers.enum';

export class CreateSiteDto {
  @IsString()
  domain: string;

  @IsString()
  @Length(1, 255)
  @IsOptional()
  title?: string;

  @IsString()
  @Length(1, 255)
  @IsOptional()
  tagline?: string;

  @IsEnum(SiteType)
  type: SiteType;

  @IsString()
  @IsOptional()
  tag?: string;

  @IsString()
  adminUser: string;

  @IsEmail()
  adminEmail: string;

  /**
   * Requires:
   * 1. 8 to 20 characters
   * 2. At least one
   * - Lowercase letter
   * - Uppercase letter
   * - Number
   * - Special character
   */
  @IsPassword()
  adminPassword: string;

  @IsEnum(Server)
  server: Server;
}
