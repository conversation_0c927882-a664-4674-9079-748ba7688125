import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DomainUtil } from 'src/common/utils/domain.util';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { User } from 'src/domain/users/entities/user.entity';
import { Site, SiteType } from '../entities/site.entity';
import { CreateSiteDto } from '../dto/create-site.dto';

export interface SiteValidationResult {
  domain: string;
  server: string;
  owner: User;
  serverIp: string;
}

@Injectable()
export class SiteValidationService {
  constructor(
    @InjectRepository(Site) private readonly siteRepo: Repository<Site>,
    @InjectRepository(User) private readonly userRepo: Repository<User>,
  ) {}

  /**
   * Validates all requirements for creating a new site
   * @param user - The requesting user
   * @param dto - Site creation data
   * @returns Promise<SiteValidationResult> - Validation result with processed data
   */
  async validateSiteCreation(
    user: RequestUser,
    dto: CreateSiteDto,
  ): Promise<SiteValidationResult> {
    const domain = this.normalizeDomain(dto.domain);
    const server = dto.server.toLowerCase();

    // Skip validation for container sites
    if (dto.type === SiteType.CONTAINER) {
      throw new BadRequestException('Container site validation not implemented');
    }

    // Validate domain format
    if (!DomainUtil.isValidDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    // Run parallel validations
    const [existingSite, owner, domainValidation] = await Promise.all([
      this.checkDomainAvailability(domain),
      this.validateUser(user.id),
      this.validateDomainPointsToServer(domain, server),
    ]);

    if (existingSite) {
      throw new ConflictException(`${domain} already deployed!`);
    }

    if (!owner) {
      throw new NotFoundException('User not found');
    }

    if (!domainValidation.isValid) {
      throw new BadRequestException(domainValidation.message!);
    }

    return {
      domain,
      server,
      owner,
      serverIp: domainValidation.serverIp,
    };
  }

  /**
   * Normalizes domain name (trim and lowercase)
   */
  private normalizeDomain(domain: string): string {
    return domain.trim().toLowerCase();
  }

  /**
   * Checks if domain is already deployed
   */
  private async checkDomainAvailability(domain: string): Promise<Site | null> {
    return this.siteRepo.findOneBy({ domain });
  }

  /**
   * Validates that user exists
   */
  private async validateUser(userId: string): Promise<User | null> {
    return this.userRepo.findOneBy({ id: userId });
  }

  /**
   * Validates that domain points to our server
   */
  private async validateDomainPointsToServer(domain: string, server: string) {
    const serverHostname = `${server}.dovely.tech`;
    return DomainUtil.validateDomainPointsToServer(domain, serverHostname);
  }
}
