import { Injectable } from '@nestjs/common';
import { encrypt, genPassword } from 'src/common/utils/crypto.util';
import { DomainUtil } from 'src/common/utils/domain.util';

export interface SiteCredentials {
  dbName: string;
  dbUser: string;
  dbPassword: string;
  encryptedDbPassword: string;
  encryptedAdminPassword: string;
}

@Injectable()
export class SiteCredentialsService {
  /**
   * Generates all credentials needed for a new site
   * @param domain - The site domain
   * @param siteId - The site ID (for generating unique db user)
   * @param adminPassword - The WordPress admin password
   * @returns SiteCredentials - All generated credentials
   */
  generateCredentials(
    domain: string,
    siteId: string,
    adminPassword: string,
  ): SiteCredentials {
    const dbName = this.generateDbName(domain);
    const dbUser = this.generateDbUser(siteId);
    const dbPassword = genPassword();
    const encryptedDbPassword = encrypt(dbPassword);
    const encryptedAdminPassword = encrypt(adminPassword);

    return {
      dbName,
      dbUser,
      dbPassword,
      encryptedDbPassword,
      encryptedAdminPassword,
    };
  }

  /**
   * Generates database name from domain
   * @param domain - The site domain
   * @returns string - Sanitized database name
   */
  private generateDbName(domain: string): string {
    return DomainUtil.sanitizeDomainForDb(domain);
  }

  /**
   * Generates unique database user from site ID
   * @param siteId - The site UUID
   * @returns string - Database username (within MySQL 32-char limit)
   */
  private generateDbUser(siteId: string): string {
    // Use first 8 characters of UUID to stay within MySQL's 32-char limit
    // wpa_ (4) + 8 chars = 12 total characters
    const shortId = siteId.substring(0, 8);
    return `wpa_${shortId}`;
  }

  /**
   * Updates database user for an existing site
   * @param siteId - The site UUID
   * @returns string - Updated database username
   */
  updateDbUser(siteId: string): string {
    return this.generateDbUser(siteId);
  }
}
