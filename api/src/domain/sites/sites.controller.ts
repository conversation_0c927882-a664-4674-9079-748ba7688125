import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { whoami } from 'src/domain/auth/decorators/whoami.decorator';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { CreateSiteDto } from './dto/create-site.dto';
import { SitesService } from './sites.service';

@Controller('sites')
export class SitesController {
  constructor(private readonly sitesService: SitesService) {}

  @Post()
  async create(@whoami() user: RequestUser, @Body() dto: CreateSiteDto) {
    return this.sitesService.create(user, dto);
  }

  @Get()
  async findAll(@whoami() user: RequestUser) {
    return this.sitesService.findAll(user);
  }

  @Get('status/:id')
  async jobStatus(@Param('id') id: string) {
    return this.sitesService.getJobStatus(id);
  }
}
