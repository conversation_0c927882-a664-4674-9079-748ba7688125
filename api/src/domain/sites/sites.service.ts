import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IWordPress } from 'src/common/interfaces/wp.interface';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { User } from 'src/domain/users/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { Repository } from 'typeorm';
import { CreateSiteDto } from './dto/create-site.dto';
import { UpdateSiteDto } from './dto/update-site.dto';
import { Site, SiteStatus, SiteType } from './entities/site.entity';
import {
  SiteCredentials,
  SiteCredentialsService,
} from './services/site-credentials.service';
import {
  SiteValidationResult,
  SiteValidationService,
} from './services/site-validation.service';

@Injectable()
export class SitesService {
  constructor(
    @InjectRepository(Site) private readonly siteRepo: Repository<Site>,
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    private readonly queueService: QueueService,
    private readonly siteValidationService: SiteValidationService,
    private readonly siteCredentialsService: SiteCredentialsService,
  ) {}

  async create(user: RequestUser, dto: CreateSiteDto) {
    if (dto.type === SiteType.CONTAINER) {
      return { message: 'Container site provision skipped.' };
    }

    const validation = await this.siteValidationService.validateSiteCreation(
      user,
      dto,
    );

    const site = this.createSiteEntity(dto, validation);
    const savedSite = await this.siteRepo.save(site);

    const credentials = this.siteCredentialsService.generateCredentials(
      validation.domain,
      savedSite.id,
      dto.adminPassword,
    );

    await this.siteRepo.update(savedSite.id, {
      dbUser: credentials.dbUser,
      dbPassword: credentials.encryptedDbPassword,
      adminPassword: credentials.encryptedAdminPassword,
    });

    try {
      const jobId = await this.provisionSite(dto, validation, credentials);
      const jobResult = await this.waitForJobCompletion(String(jobId));

      if (!jobResult) {
        await this.cleanupFailedSite(savedSite.id);
        throw new UnprocessableEntityException('Provisioning job failed');
      }

      await this.siteRepo.update(savedSite.id, { status: SiteStatus.ACTIVE });

      return {
        site: {
          ...savedSite,
          status: SiteStatus.ACTIVE,
          dbUser: credentials.dbUser,
        },
        jobId,
      };
    } catch (error) {
      await this.cleanupFailedSite(savedSite.id);

      if (error instanceof Error) {
        throw new UnprocessableEntityException(
          error.message ?? 'Provisioning failed',
        );
      }
      throw error;
    }
  }

  async findAll(user: RequestUser) {
    return await this.siteRepo.find({
      where: { owner: { id: user.id } },
      relations: { owner: true },
    });
  }

  async findOne(id: string, user: RequestUser) {
    return await this.siteRepo.findOneOrFail({
      where: { id, owner: { id: user.id } },
      relations: { owner: true },
    });
  }

  async update(id: string, user: RequestUser, dto: UpdateSiteDto) {
    const site = await this.findOne(id, user);

    const updatedSite = this.siteRepo.create({
      ...site,
      title: dto.title ?? site.title,
      tagline: dto.tagline ?? site.tagline,
      tag: dto.tag ?? site.tag,
    });

    return this.siteRepo.save(updatedSite);
  }

  async delete(id: string, user: RequestUser) {
    const site = await this.findOne(id, user);

    // Add cleanup logic here if needed

    await this.siteRepo.remove(site);
    return { message: 'Site deleted successfully' };
  }

  async getJobStatus(id: string): Promise<{
    id: string;
    state: string;
    progress: number | object;
    result: unknown;
    failedReason?: string;
  } | null> {
    return await this.queueService.getJobStatus(id);
  }

  /**
   * Creates a site entity with initial data
   */
  private createSiteEntity(
    dto: CreateSiteDto,
    validation: SiteValidationResult,
  ): Site {
    return this.siteRepo.create({
      ...dto,
      domain: validation.domain,
      tag: dto.tag || dto.type,
      dbName: '',
      dbUser: '',
      dbPassword: '',
      adminPassword: '',
      dbHost: 'localhost',
      owner: validation.owner,
      status: SiteStatus.PENDING,
    });
  }

  /**
   * Provisions a site by enqueueing the WordPress job
   * @returns string - The provision queue job ID
   */
  private async provisionSite(
    dto: CreateSiteDto,
    validation: SiteValidationResult,
    credentials: SiteCredentials,
  ): Promise<string> {
    const payload: IWordPress = {
      domain: validation.domain,
      title: dto.title || 'Ultra-fast hosting from Onclouds',
      adminUser: dto.adminUser,
      adminEmail: dto.adminEmail,
      adminPassword: dto.adminPassword,
      dbName: credentials.dbName,
      dbUser: credentials.dbUser,
      dbPassword: credentials.dbPassword,
      server: dto.server,
    };

    const { jobId } = await this.queueService.enqueueWP(payload);
    return String(jobId);
  }

  /**
   * Cleans up a failed site creation
   */
  private async cleanupFailedSite(siteId: string): Promise<void> {
    try {
      await this.siteRepo.delete(siteId);
    } catch (deleteError) {
      console.error(
        'Failed to cleanup site after provision error:',
        deleteError,
      );
    }
  }

  private async waitForJobCompletion(
    jobId: string,
    maxAttempts = 30,
  ): Promise<unknown> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      const status = await this.queueService.getJobStatus(jobId);

      if (!status) {
        throw new UnprocessableEntityException('Job not found');
      }

      if (status.state === 'completed') {
        return status.result;
      }

      if (status.state === 'failed') {
        throw new UnprocessableEntityException(
          status.failedReason ?? 'Job failed',
        );
      }

      // Wait before checking again
      await new Promise((resolve) => setTimeout(resolve, 2000));
      attempts++;
    }

    throw new UnprocessableEntityException('Job timed out');
  }
}
