import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { resolve } from 'dns/promises';
import { IWordPress } from 'src/common/interfaces/wp.interface';
import { encrypt, genPassword } from 'src/common/utils/crypto.util';
import { RequestUser } from 'src/domain/auth/interfaces/request-user.interface';
import { User } from 'src/domain/users/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { Repository } from 'typeorm';
import { CreateSiteDto } from './dto/create-site.dto';
import { UpdateSiteDto } from './dto/update-site.dto';
import { Site, SiteStatus, SiteType } from './entities/site.entity';

@Injectable()
export class SitesService {
  constructor(
    @InjectRepository(Site) private readonly siteRepo: Repository<Site>,
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    private readonly queueService: QueueService,
  ) {}

  async create(user: RequestUser, dto: CreateSiteDto) {
    const domain = dto.domain.trim().toLowerCase();
    const server = dto.server.toLowerCase();

    if (dto.type === SiteType.CONTAINER) {
      return { message: 'Container site provision skipped.' };
    }

    const [existingSite, owner, isReachable] = await Promise.all([
      this.siteRepo.findOneBy({ domain }),
      this.userRepo.findOneBy({ id: user.id }),
      this.resolveDomain(domain),
    ]);

    if (existingSite) {
      throw new ConflictException(`${domain} already deployed!`);
    }

    if (!owner) {
      throw new NotFoundException('User not found');
    }

    if (!isReachable) {
      throw new BadRequestException(
        `Domain is not reachable. Please point your CNAME to ${server}.dovely.tech`,
      );
    }

    // Generate credentials
    const dbName = domain.replace(/\./g, '_');
    const dbPassword = genPassword();
    const adminPassword = encrypt(dto.adminPassword);
    const encDbPassword = encrypt(dbPassword);

    const site = this.siteRepo.create({
      ...dto,
      domain,
      tag: dto.tag || dto.type,
      adminPassword,
      dbName,
      dbUser: '',
      dbPassword: encDbPassword,
      dbHost: 'localhost',
      owner,
      status: SiteStatus.PENDING,
    });

    const savedSite = await this.siteRepo.save(site);

    const dbUser = `wpa_${savedSite.id}`;
    await this.siteRepo.update(savedSite.id, { dbUser });

    try {
      const payload: IWordPress = {
        domain,
        title: dto.title || site.title,
        adminUser: dto.adminUser,
        adminEmail: dto.adminEmail,
        adminPassword: dto.adminPassword,
        dbName,
        dbUser,
        dbPassword,
        server: dto.server,
      };
      const { jobId } = await this.queueService.enqueueWP(payload);

      const jobResult: unknown = await this.waitForJobCompletion(String(jobId));
      if (!jobResult) await this.siteRepo.delete(savedSite.id);
      await this.siteRepo.update(savedSite.id, { status: SiteStatus.ACTIVE });

      return { site: { ...savedSite, status: SiteStatus.ACTIVE }, jobId };
    } catch (error) {
      if (error instanceof Error)
        throw new UnprocessableEntityException(
          error.message ?? 'Provisioning failed',
        );
      throw error;
    }
  }

  async findAll(user: RequestUser) {
    return await this.siteRepo.find({
      where: { owner: { id: user.id } },
      relations: { owner: true },
    });
  }

  async findOne(id: string, user: RequestUser) {
    return await this.siteRepo.findOneOrFail({
      where: { id, owner: { id: user.id } },
      relations: { owner: true },
    });
  }

  async update(id: string, user: RequestUser, dto: UpdateSiteDto) {
    const site = await this.findOne(id, user);

    // Only allow updating certain fields
    const updatedSite = this.siteRepo.create({
      ...site,
      title: dto.title ?? site.title,
      tagline: dto.tagline ?? site.tagline,
      tag: dto.tag ?? site.tag,
    });

    return this.siteRepo.save(updatedSite);
  }

  async delete(id: string, user: RequestUser) {
    const site = await this.findOne(id, user);

    // Add cleanup logic here if needed

    await this.siteRepo.remove(site);
    return { message: 'Site deleted successfully' };
  }

  async getJobStatus(id: string): Promise<{
    id: string;
    state: string;
    progress: number | object;
    result: unknown;
    failedReason?: string;
  } | null> {
    return await this.queueService.getJobStatus(id);
  }

  private async waitForJobCompletion(
    jobId: string,
    maxAttempts = 30,
  ): Promise<unknown> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      const status = await this.getJobStatus(jobId);

      if (!status) {
        throw new UnprocessableEntityException('Job not found');
      }

      if (status.state === 'completed') {
        return status.result;
      }

      if (status.state === 'failed') {
        throw new UnprocessableEntityException(
          status.failedReason ?? 'Job failed',
        );
      }

      // Wait before checking again
      await new Promise((resolve) => setTimeout(resolve, 2000));
      attempts++;
    }

    throw new UnprocessableEntityException('Job timed out');
  }

  private async resolveDomain(domain: string): Promise<boolean> {
    try {
      const addresses = await resolve(domain);
      return addresses.length > 0;
    } catch {
      return false;
    }
  }
}
