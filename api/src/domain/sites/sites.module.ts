import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/domain/users/entities/user.entity';
import { QueueModule } from 'src/queue/queue.module';
import { Site } from './entities/site.entity';
import { SitesController } from './sites.controller';
import { SitesService } from './sites.service';

@Module({
  imports: [TypeOrmModule.forFeature([Site, User]), QueueModule],
  controllers: [SitesController],
  providers: [SitesService],
})
export class SitesModule {}
