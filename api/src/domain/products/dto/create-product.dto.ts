import {
  ArrayNotEmpty,
  ArrayUnique,
  IsOptional,
  Length,
} from 'class-validator';
import { IsCurrency } from 'src/common/decorators/validators/is-currency.decorator';
import { IsEntity } from 'src/common/decorators/validators/is-entity.decorator';
import { IdDto } from 'src/common/dto/id.dto';
import { IdentifierFn } from 'src/common/utils/id.util';

export class CreateProductDto {
  @Length(2, 50)
  readonly name: string;

  @IsOptional()
  @Length(0, 500)
  readonly description?: string;

  @IsCurrency()
  readonly price: number;

  @ArrayNotEmpty()
  @ArrayUnique(IdentifierFn.ID_DTO)
  @IsEntity()
  readonly categories: IdDto[];
}
