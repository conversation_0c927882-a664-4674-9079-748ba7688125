import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOkResponse } from '@nestjs/swagger';
import { IdDto } from 'src/common/dto/id.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { Public } from 'src/domain/auth/decorators/public.decorator';
import { Roles } from 'src/domain/auth/decorators/role.decorator';
import { Role } from 'src/domain/auth/roles/role.enum';
import { IdFileNameDto } from 'src/files/dto/id-filename.dto';
import { BodyInterceptor } from 'src/files/interceptors/body/body.interceptor';
import { FileSchema } from 'src/files/swagger/schemas/file.schema';
import {
  MaxFileCount,
  MULTIPART_FORMDATA_KEY,
} from 'src/files/utils/constants.util';
import { createParseFilePipe } from 'src/files/utils/files.util';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductsService } from './products.service';

@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Roles(Role.MANAGER)
  @Post()
  create(@Body() dto: CreateProductDto) {
    return this.productsService.create(dto);
  }

  @Public()
  @Get()
  findAll(@Query() pagination: PaginationDto) {
    return this.productsService.findAll(pagination);
  }

  @Public()
  @Get(':id')
  findOne(@Param() { id }: IdDto) {
    return this.productsService.findOne(id);
  }

  @Roles(Role.MANAGER)
  @Patch(':id')
  update(@Param() { id }: IdDto, @Body() dto: UpdateProductDto) {
    return this.productsService.update(id, dto);
  }

  @Roles(Role.MANAGER)
  @Delete(':id')
  remove(@Param() { id }: IdDto) {
    return this.productsService.remove(id);
  }

  @ApiConsumes(MULTIPART_FORMDATA_KEY)
  @ApiBody({ type: FileSchema })
  @Roles(Role.MANAGER)
  @UseInterceptors(
    FileInterceptor('src/files', {
      limits: { files: MaxFileCount.PRODUCT_IMAGES },
    }),
    BodyInterceptor,
  )
  @Post(':id/images')
  uploadImages(
    @Param() { id }: IdDto,
    @UploadedFiles(createParseFilePipe('2mb', 'png', 'jpeg', 'webp', 'svg'))
    file: Express.Multer.File[],
  ) {
    return this.productsService.uploadImages(id, file);
  }

  @ApiOkResponse({ type: FileSchema })
  @Public()
  @Get(':id/images/:filename')
  downloadImage(@Param() { id, filename }: IdFileNameDto) {
    return this.productsService.downloadImage(id, filename);
  }

  @Roles(Role.MANAGER)
  @Delete(':id/images/:filename')
  deleteImage(@Param() { id, filename }: IdFileNameDto) {
    return this.productsService.deleteImage(id, filename);
  }
}
