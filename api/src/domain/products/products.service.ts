import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { pathExists } from 'fs-extra';
import { join } from 'path';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { DefaultPageSize } from 'src/common/utils/constants.util';
import { Product } from 'src/domain/products/entities/product.entity';
import { StorageService } from 'src/files/storage/storage.service';
import {
  FilePath,
  MaxFileCount,
  UPLOAD_DIR,
} from 'src/files/utils/constants.util';
import { Repository } from 'typeorm';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    private readonly storageService: StorageService,
  ) {}
  create(dto: CreateProductDto) {
    const product = this.productRepo.create(dto);
    return this.productRepo.save(product);
  }

  findAll(pagination: PaginationDto) {
    return this.productRepo.find({
      skip: pagination.offset,
      take: pagination.limit ?? DefaultPageSize.PRODUCTS,
    });
  }

  async findOne(id: string) {
    return await this.productRepo.findOneByOrFail({ id });
  }

  async update(id: string, dto: UpdateProductDto) {
    const product = await this.productRepo.preload({ id, ...dto });
    if (!product) throw new NotFoundException('Product not found');
    return this.productRepo.save(product);
  }

  async remove(id: string) {
    const product = await this.findOne(id);

    await this.productRepo.remove(product);
    await this.deleteBaseDir(id);

    return product;
  }

  async uploadImages(id: string, files: Express.Multer.File[]) {
    await this.findOne(id);

    const { BASE, IMAGES } = FilePath.Products;
    const path = join(BASE, id, IMAGES);

    if (await pathExists(join(UPLOAD_DIR, path))) {
      const uploadFileCount = files.length;
      const dirFileCount = await this.storageService.getDirFileCount(path);
      const totalFileCount = uploadFileCount + dirFileCount;

      this.storageService.validateFileCount(
        totalFileCount,
        MaxFileCount.PRODUCT_IMAGES,
      );
    }

    await this.storageService.createDir(path);

    await Promise.all(
      files.map(async (file) => await this.storageService.saveFile(path, file)),
    );
  }

  async downloadImage(id: string, filename: string) {
    await this.findOne(id);

    const { BASE, IMAGES } = FilePath.Products;
    const path = join(BASE, id, IMAGES, filename);

    await this.storageService.validatePath(path);

    return this.storageService.getFile(path);
  }

  async deleteImage(id: string, filename: string) {
    await this.findOne(id);

    const { BASE, IMAGES } = FilePath.Products;
    const path = join(BASE, id, IMAGES, filename);

    await this.storageService.validatePath(path);

    return await this.storageService.delete(path);
  }

  private deleteBaseDir(id: string) {
    const { BASE } = FilePath.Products;
    const path = join(BASE, id);
    return this.storageService.delete(path);
  }
}
