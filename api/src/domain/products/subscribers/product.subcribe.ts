import { pathExists } from 'fs-extra';
import { join } from 'path';
import { Product } from 'src/domain/products/entities/product.entity';
import { StorageService } from 'src/files/storage/storage.service';
import { FilePath, UPLOAD_DIR } from 'src/files/utils/constants.util';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
} from 'typeorm';

@EventSubscriber()
export class ProductsSubscriber implements EntitySubscriberInterface<Product> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly storageService: StorageService,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return Product;
  }

  async afterLoad(entity: Product) {
    const imagesFilenames = await this.getImagesFilenames(entity.id);
    entity[this.IMAGES_FILENAMES_KEY] = imagesFilenames;
  }

  private readonly IMAGES_FILENAMES_KEY = 'images';

  private async getImagesFilenames(id: string) {
    const { BASE, IMAGES } = FilePath.Products;
    const path = join(BASE, id, IMAGES);

    if (!(await pathExists(join(UPLOAD_DIR, path)))) return;

    return this.storageService.getDirFiles(path);
  }
}
