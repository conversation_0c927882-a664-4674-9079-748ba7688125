import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { DefaultPageSize } from 'src/common/utils/constants.util';
import { Repository } from 'typeorm';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepo: Repository<Category>,
  ) {}

  create(dto: CreateCategoryDto) {
    const category = this.categoryRepo.create(dto);
    return this.categoryRepo.save(category);
  }

  findAll(pagination: PaginationDto) {
    return this.categoryRepo.find({
      skip: pagination.offset,
      take: pagination.limit ?? DefaultPageSize.CATEGORY,
    });
  }

  async findOne(id: string) {
    return await this.categoryRepo.findOneOrFail({
      where: { id },
      relations: { products: true },
    });
  }

  async update(id: string, dto: UpdateCategoryDto) {
    const category = await this.categoryRepo.preload({ id, ...dto });
    if (!category) throw new NotFoundException('Category not found');
    return this.categoryRepo.save(category);
  }

  async remove(id: string) {
    const category = await this.findOne(id);
    if (category.products.length)
      throw new ConflictException('Category has associated products');
    return this.categoryRepo.remove(category);
  }
}
