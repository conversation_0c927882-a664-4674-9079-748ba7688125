import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { DefaultPageSize } from 'src/common/utils/constants.util';
import { Product } from 'src/domain/products/entities/product.entity';
import { Repository } from 'typeorm';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderItemDto } from './dto/order-item.dto';
import { OrderItem } from './entities/order-item.entity';
import { Order } from './entities/order.entity';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepo: Repository<OrderItem>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
  ) {}

  async create(dto: CreateOrderDto) {
    const { items } = dto;

    const itemsWithPrice = await Promise.all(
      items.map((item) => this.createOrderItemWithPrice(item)),
    );

    const order = this.orderRepo.create({ ...dto, items: itemsWithPrice });
    return this.orderRepo.save(order);
  }

  findAll(pagination: PaginationDto) {
    return this.orderRepo.find({
      skip: pagination.offset,
      take: pagination.limit ?? DefaultPageSize.ORDERS,
    });
  }

  async findOne(id: string) {
    return await this.orderRepo.findOneOrFail({
      where: { id },
      relations: { items: { product: true }, customer: true, payment: true },
    });
  }

  async remove(id: string) {
    const order = await this.findOne(id);
    return this.orderRepo.remove(order);
  }

  private async createOrderItemWithPrice(orderItemDto: OrderItemDto) {
    const { id } = orderItemDto.product;
    const { price } = await this.productRepo.findOneByOrFail({ id });
    const orderItem = this.orderItemRepo.create({ ...orderItemDto, price });
    return orderItem;
  }
}
