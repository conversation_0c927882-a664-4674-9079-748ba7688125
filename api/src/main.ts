import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as cookieParser from 'cookie-parser';
import helmet from 'helmet';
import pkg from '../package.json';
import { AppModule } from './app.module';
import { parsedEnv } from './env/env.schema';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors();
  app.use(cookieParser());
  app.use(helmet());
  app.setGlobalPrefix(parsedEnv.APP_PREFIX);

  const title =
    pkg?.name
      ?.split('-')
      ?.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      ?.join(' ') || 'API';

  const config = new DocumentBuilder()
    .setTitle(title)
    .setDescription(pkg?.description)
    .setVersion(pkg?.version)
    .addBearerAuth()
    .addSecurityRequirements('bearer')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document);

  await app.listen(parsedEnv.PORT ?? 4222);
}
bootstrap();
