import { Body, Controller, Post } from '@nestjs/common';
import { Public } from 'src/domain/auth/decorators/public.decorator';
import { Server } from 'src/domain/sites/enums/servers.enum';
import { SshService } from './ssh.service';

@Controller('ssh')
export class SshController {
  constructor(private readonly sshService: SshService) {}

  @Public()
  @Post('connect')
  async healthCheck(@Body('server') server: Server) {
    const output = await this.sshService.checkHealth(server);
    return { output };
  }
}
