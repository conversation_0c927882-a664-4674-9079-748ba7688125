import { Module } from '@nestjs/common';

import { CommonModule } from './common/common.module';
import { DbModule } from './db/db.module';
import { AuthModule } from './domain/auth/auth.module';
import { CategoriesModule } from './domain/categories/categories.module';
import { LogsModule } from './domain/logs/logs.module';
import { OrdersModule } from './domain/orders/orders.module';
import { PaymentsModule } from './domain/payments/payments.module';
import { ProductsModule } from './domain/products/products.module';
import { SitesModule } from './domain/sites/sites.module';
import { UsersModule } from './domain/users/users.module';
import { EnvModule } from './env/env.module';
import { FilesModule } from './files/files.module';
import { SshModule } from './ssh/ssh.module';
import { StaticModule } from './static/static.module';
import { QueueModule } from './queue/queue.module';

@Module({
  imports: [
    AuthModule,
    CategoriesModule,
    CommonModule,
    DbModule,
    EnvModule,
    FilesModule,
    LogsModule,
    OrdersModule,
    PaymentsModule,
    ProductsModule,
    SitesModule,
    SshModule,
    StaticModule,
    UsersModule,
    QueueModule,
  ],
})
export class AppModule {}
