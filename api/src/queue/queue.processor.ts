import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { IWordPress } from 'src/common/interfaces/wp.interface';
import { Server } from 'src/domain/sites/enums/servers.enum';
import { SshService } from 'src/ssh/ssh.service';

@Processor('ssh')
export class QueueProcessor {
  constructor(private readonly sshService: SshService) {}

  @Process('provision-wp')
  async handleProvision(job: Job<IWordPress>) {
    try {
      return await this.sshService.provisionWP(job.data);
    } catch (error) {
      console.error(`❌ [SSH Job Failed] ID: ${job.id}`, error);
      throw error;
    }
  }

  @Process('check-health')
  async handleHealthCheck(job: Job<{ server: Server }>) {
    try {
      const result = await this.sshService.checkHealth(job.data.server);
      return result;
    } catch (error) {
      console.error(`❌ Health check failed for ${job.data.server}`, error);
      throw error;
    }
  }
}
