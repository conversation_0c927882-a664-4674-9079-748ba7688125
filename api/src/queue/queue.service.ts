import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { IWordPress } from 'src/common/interfaces/wp.interface';

@Injectable()
export class QueueService {
  constructor(@InjectQueue('ssh') private readonly sshQueue: Queue) {}

  async enqueueWP(payload: IWordPress) {
    const job = await this.sshQueue.add('provision-wp', payload, {
      attempts: 3,
      backoff: { type: 'fixed', delay: 5000 },
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: false,
    });

    return { jobId: job.id };
  }

  async getJobStatus(id: string): Promise<{
    id: string;
    state: string;
    progress: number | object;
    result: unknown;
    failedReason?: string;
  } | null> {
    const job = await this.sshQueue.getJob(id);
    if (!job) return null;

    const state = (await job.getState()) as string;
    const progress = job.progress() as number | object;
    const result = job.returnvalue as unknown;
    const failedReason = job.failedReason;

    return { id, state, progress, result, failedReason };
  }
}
