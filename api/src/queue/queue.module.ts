import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { AppConfig } from 'src/env/env.schema';
import { SshModule } from 'src/ssh/ssh.module';
import { QueueProcessor } from './queue.processor';
import { QueueService } from './queue.service';

@Module({
  imports: [
    BullModule.forRootAsync({
      inject: [AppConfig],
      useFactory: (config: AppConfig) => ({
        redis: {
          host: config.REDIS_HOST,
          port: config.REDIS_PORT,
          ...(config.REDIS_PASSWORD && { password: config.REDIS_PASSWORD }),
        },
      }),
    }),
    BullModule.registerQueue({ name: 'ssh' }),
    SshModule,
  ],
  providers: [QueueService, QueueProcessor],
  exports: [QueueService],
})
export class QueueModule {}
