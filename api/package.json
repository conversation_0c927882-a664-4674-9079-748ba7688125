{"name": "onclouds-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:create": "bun typeorm migration:create", "premigration:generate": "bun run build", "migration:generate": "bun typeorm migration:generate -d dist/src/db/data-source -p", "premigration:run": "bun run build", "migration:run": "bun typeorm migration:run -d dist/src/db/data-source", "migration:revert": "bun typeorm migration:revert -d dist/src/db/data-source", "seed": "ts-node -r tsconfig-paths/register src/db/seeding/seed.ts", "db:reset": "ts-node -r tsconfig-paths/register src/db/reset.ts"}, "dependencies": {"@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/cookie-parser": "^1.4.9", "argon2": "^0.43.0", "bull": "^4.16.5", "bytes": "^3.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "dotenv-expand": "^12.0.2", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "magic-bytes.js": "^1.12.1", "mime-types": "^3.0.1", "multer": "^2.0.1", "node-ssh": "^13.2.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ssh2": "^1.16.0", "typeorm": "^0.3.25"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "^9.9.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bytes": "^3.1.5", "@types/ed25519": "^0.0.3", "@types/express": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/mime-types": "^3.0.1", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/ssh2": "^1.15.5", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typeorm-extension": "^3.7.1", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0", "zod": "^3.25.67"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}