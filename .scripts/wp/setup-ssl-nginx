#!/bin/bash
set -euo pipefail
source "$1"

NGINX_TEMPLATE="/etc/nginx/sites-available/template"
NGINX_CONF="/etc/nginx/sites-available/$DOMAIN"
NGINX_LINK="/etc/nginx/sites-enabled/$DOMAIN"
SSL_PATH="/etc/letsencrypt/live/$DOMAIN"

if [ ! -d "$SSL_PATH" ]; then
    sudo certbot certonly --nginx \
        -d "$DOMAIN" \
        -d "www.$DOMAIN" \
        --non-interactive \
        --agree-tos \
        -m "$ADMIN_EMAIL" || {
        exit 1
    }
fi

if [ ! -f "$NGINX_CONF" ]; then
    sudo cp "$NGINX_TEMPLATE" "$NGINX_CONF"
    sudo sed -i "s|{{DOMAIN}}|$DOMAIN|g" "$NGINX_CONF"
    sudo ln -sf "$NGINX_CONF" "$NGINX_LINK"
    sudo nginx -t && sudo systemctl reload nginx
fi
