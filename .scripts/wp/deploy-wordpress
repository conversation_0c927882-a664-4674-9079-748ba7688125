#!/bin/bash
set -euo pipefail
source "$1"

SITES_DIR="/home/<USER>/sites/$DOMAIN/public"
WP_CLI="wp --path=$SITES_DIR"

if [ ! -f "$SITES_DIR/wp-config-sample.php" ]; then
  $WP_CLI core download
fi

$WP_CLI config create \
  --dbname="$DB_NAME" \
  --dbuser="$DB_USER" \
  --dbpass="$DB_PASSWORD" \
  --skip-check

$WP_CLI core install \
  --url="https://$DOMAIN" \
  --title="$TITLE" \
  --admin_user="$ADMIN_USER" \
  --admin_email="$ADMIN_EMAIL" \
  --admin_password="$ADMIN_PASSWORD"

find "$SITES_DIR" -type d -exec chmod 755 {} \;
find "$SITES_DIR" -type f -exec chmod 644 {} \;
chmod 600 "$SITES_DIR/wp-config.php"
