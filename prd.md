# 🧾 Product Requirements Document (PRD)

## Project: WordPress Provisioning Platform (Onclouds)

---

## 🎯 Objective

Build a secure, scalable, multi-tenant API platform that allows authenticated users to provision WordPress websites on demand over SSH, complete with:

- SSL certificates via Certbot
- MySQL database setup
- Nginx config templating
- WordPress installation via wp-cli
- Site metadata storage in PostgreSQL
- Job queuing via BullMQ

---

## ✅ Features Completed

### 🔐 Authentication

- [x] JWT Auth with Passport (HTTP-only cookies)
- [x] Authenticated endpoints default, with `@Public()` decorator

### 🧑 Users

- [x] In-memory user store
- [x] Registration/login with hashed password (using `HashingService`)

### 🖥 Server Setup

- [x] VPS (HostBrr) running Debian
- [x] User `onclouds` owns `/home/<USER>/sites`
- [x] SSH key-based auth via ED25519
- [x] Hostname: `${server}.dovely.tech`

### 🔌 SSH Service

- [x] Uses `ssh2` for per-request SSH connections
- [x] Reads private key from AppConfig
- [x] `checkHealth()` command returns uptime
- [x] `execCommand()` wrapped with error handling
- [x] No shared client state (stateless)

### 🪄 WordPress Provisioning

- [x] Shell script `/scripts/provision-wp`
  - Reads from `.secrets/${domain}.env`
  - Sets up:
    - Directories (`public`, `cache`, `logs`)
    - SSL (via Certbot)
    - Nginx config (template-based)
    - DB creation with privileges
    - wp-cli install
  - Returns JSON with site info

### 🗃 Site Management

- [x] PostgreSQL entity `Site`
- [x] Stores admin, DB info, owner (relation), etc.
- [x] `createSite()` endpoint validates domain & starts queue job

### ⚙️ Queues

- [x] BullMQ (`ssh` queue)
- [x] `enqueueWP()` and `getJobStatus()`
- [x] `SshProcessor` runs `provisionWP()` method
- [x] Status + progress tracking

---

## 🚧 In Progress

### 📡 API Controllers

- [x] Create Site (done)
- [ ] Expose `GET /sites/:id/status`
- [ ] Expose `GET /sites` (list user's sites)
- [ ] Expose `GET /ssh/health/:server` (currently direct call)

### 🔧 Scripts

- [x] `provision-wp` (Bash)
- [ ] Add error code exit traps & output pipefail-friendly logs
- [ ] Support both `.env` and `.cnf` formats if needed

---

## 📝 To Do for MVP

- [ ] Improve Nginx template DSL (`{{DOMAIN}}` => `{{domain}}`?)
- [ ] Schedule auto-renew for certbot
- [ ] Create frontend (Next.js dashboard)
- [ ] Proper DB credentials encryption + secret vault integration
- [ ] Rate-limit SSH provisioning jobs per user
- [ ] Add rollback if any step fails
- [ ] Add audit logs for all operations
- [ ] Add metrics / job monitoring UI

---

## 🤖 Stack Summary

| Layer        | Tech                            |
| ------------ | ------------------------------- |
| API Server   | NestJS + TypeORM (PostgreSQL)   |
| SSH Library  | ssh2 (with ED25519 key support) |
| Queue System | BullMQ                          |
| OS Scripts   | Bash + wp-cli                   |
| Web Server   | Nginx + Certbot                 |
| DB (Site)    | PostgreSQL                      |
| DB (Site WP) | MySQL                           |

---

## 🧠 Design Principles

- **Per-request SSH**: No shared clients, concurrency-safe
- **Job queue abstraction**: Site provisioning is always backgrounded
- **Secrets separation**: No sensitive info logged
- **Modular and DRY**: All configs and secrets read from `.env`, templated Nginx, reusable SSH logic

---

## 📌 Decisions

- Use `.env` for secrets (sourced in scripts)
- Use `/home/<USER>/sites/${domain}` layout
- Certbot runs in non-interactive mode
- SSH host is derived via `${server}.${SSH_HOST}`

---

## ✅ MVP Goal

Provision a WordPress site **via one POST request**:

- Domain is resolved
- SSL is issued
- DB + Nginx + WP is set up
- Site is saved in DB
- Status is queryable
